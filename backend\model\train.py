import os
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import cv2
from sklearn.model_selection import KFold

import glob
from PIL import Image
import argparse
from autoencoder import build_autoencoder, get_callbacks

def apply_clahe(image):
    """
    Áp dụng CLAHE (Contrast Limited Adaptive Histogram Equalization) cho ảnh X-ray

    Args:
        image: Ảnh đầu vào (numpy array)

    Returns:
        image: Ảnh sau khi áp dụng CLAHE
    """
    # Chuyển về uint8 nếu cần
    if image.dtype != np.uint8:
        image = (image * 255).astype(np.uint8)

    # Áp dụng CLAHE
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced_image = clahe.apply(image)

    # Chuyển về float32 và normalize về [0,1]
    return enhanced_image.astype(np.float32) / 255.0

def advanced_normalize(image):
    """
    Cải thiện normalization: MinMax + Z-score normalization

    Args:
        image: Ảnh đầu vào

    Returns:
        image: Ảnh sau khi normalize
    """
    # MinMax normalization về [0,1]
    image = (image - image.min()) / (image.max() - image.min() + 1e-8)

    # Z-score normalization
    mean = image.mean()
    std = image.std()
    if std > 1e-8:  # Tránh chia cho 0
        image = (image - mean) / std

    # Clip về [-3, 3] để tránh outliers
    image = np.clip(image, -3, 3)

    # Rescale về [0,1]
    image = (image + 3) / 6

    return image

def resize_with_aspect_ratio(image, target_size):
    """
    Resize ảnh với việc bảo toàn tỷ lệ khung hình

    Args:
        image: PIL Image
        target_size: Kích thước mục tiêu (int)

    Returns:
        image: PIL Image đã resize
    """
    # Lấy kích thước hiện tại
    width, height = image.size

    # Tính tỷ lệ scale
    scale = min(target_size / width, target_size / height)

    # Tính kích thước mới
    new_width = int(width * scale)
    new_height = int(height * scale)

    # Resize ảnh
    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # Tạo ảnh mới với kích thước target và paste ảnh đã resize vào giữa
    new_image = Image.new('L', (target_size, target_size), 0)
    paste_x = (target_size - new_width) // 2
    paste_y = (target_size - new_height) // 2
    new_image.paste(image, (paste_x, paste_y))

    return new_image

def load_and_preprocess_data(data_dir, img_size=128, batch_size=32):
    """
    Tải và tiền xử lý dữ liệu từ thư mục - CHỈ SỬ DỤNG ẢNH NORMAL

    Args:
        data_dir: Đường dẫn đến thư mục chứa thư mục 'train' và 'val' (ví dụ: 'C:\\DeepLearning\\AE_Xray\\backend\\dataset')
        img_size: Kích thước ảnh đầu vào
        batch_size: Kích thước batch

    Returns:
        train_generator, val_generator, val_images: Data generators cho train và validation, và val_images để tính threshold
    """
    # Lấy ảnh NORMAL từ thư mục train/NORMAL/
    train_normal_pattern = os.path.join(data_dir, 'train', 'NORMAL', '*.jpeg')
    train_normal_images = glob.glob(train_normal_pattern)
    print(f"Tìm thấy {len(train_normal_images)} ảnh NORMAL cho huấn luyện")

    # Lấy ảnh NORMAL từ thư mục val/NORMAL/
    val_normal_pattern = os.path.join(data_dir, 'val', 'NORMAL', '*.jpeg')
    val_normal_images = glob.glob(val_normal_pattern)
    print(f"Tìm thấy {len(val_normal_images)} ảnh NORMAL cho validation")

    if not train_normal_images:
        print(f"Cảnh báo: Không tìm thấy ảnh NORMAL cho huấn luyện tại {train_normal_pattern}")
        return None, None, None

    if not val_normal_images:
        print(f"Cảnh báo: Không tìm thấy ảnh NORMAL cho validation tại {val_normal_pattern}")
        return None, None, None

    # Tải và tiền xử lý ảnh training (NORMAL) với CLAHE và advanced normalization
    train_images = []
    for img_path in train_normal_images:
        try:
            img = Image.open(img_path).convert('L')  # Chuyển sang ảnh xám
            img = resize_with_aspect_ratio(img, img_size)  # Resize với bảo toàn tỷ lệ
            img_array = np.array(img)

            # Áp dụng CLAHE
            img_array = apply_clahe(img_array)

            # Áp dụng advanced normalization
            img_array = advanced_normalize(img_array)

            train_images.append(img_array)
        except Exception as e:
            print(f"Lỗi khi xử lý ảnh training {img_path}: {e}")

    # Tải và tiền xử lý ảnh validation (NORMAL) với CLAHE và advanced normalization
    val_images = []
    for img_path in val_normal_images:
        try:
            img = Image.open(img_path).convert('L')  # Chuyển sang ảnh xám
            img = resize_with_aspect_ratio(img, img_size)  # Resize với bảo toàn tỷ lệ
            img_array = np.array(img)

            # Áp dụng CLAHE
            img_array = apply_clahe(img_array)

            # Áp dụng advanced normalization
            img_array = advanced_normalize(img_array)

            val_images.append(img_array)
        except Exception as e:
            print(f"Lỗi khi xử lý ảnh validation {img_path}: {e}")

    train_images = np.array(train_images)
    val_images = np.array(val_images)

    train_images = np.expand_dims(train_images, axis=-1)  # Thêm chiều kênh
    val_images = np.expand_dims(val_images, axis=-1)  # Thêm chiều kênh

    # Tăng cường dữ liệu với các kỹ thuật cải tiến cho ảnh X-ray
    train_datagen = ImageDataGenerator(
        rotation_range=15,          # Tăng rotation range
        width_shift_range=0.15,     # Tăng shift range
        height_shift_range=0.15,
        zoom_range=0.15,            # Tăng zoom range
        horizontal_flip=True,
        brightness_range=[0.8, 1.2], # Thêm brightness augmentation
        fill_mode='constant',       # Fill với giá trị constant
        cval=0.0                    # Fill với màu đen
    )

    val_datagen = ImageDataGenerator()

    # Tạo generators
    train_generator = train_datagen.flow(
        train_images, train_images,
        batch_size=batch_size
    )

    val_generator = val_datagen.flow(
        val_images, val_images,
        batch_size=batch_size
    )

    return train_generator, val_generator, val_images

def train_with_kfold_cv(data_dir, img_size=128, batch_size=32, epochs=50, n_splits=5, output_dir='./saved_model'):
    """
    Huấn luyện mô hình với K-fold Cross Validation

    Args:
        data_dir: Đường dẫn đến dataset
        img_size: Kích thước ảnh
        batch_size: Batch size
        epochs: Số epochs
        n_splits: Số folds cho cross validation
        output_dir: Thư mục lưu kết quả

    Returns:
        best_model: Mô hình tốt nhất
        cv_results: Kết quả cross validation
    """
    # Load tất cả dữ liệu NORMAL
    train_normal_pattern = os.path.join(data_dir, 'train', 'NORMAL', '*.jpeg')
    val_normal_pattern = os.path.join(data_dir, 'val', 'NORMAL', '*.jpeg')

    all_normal_images = glob.glob(train_normal_pattern) + glob.glob(val_normal_pattern)
    print(f"Tổng số ảnh NORMAL cho K-fold CV: {len(all_normal_images)}")

    # Load và preprocess tất cả ảnh
    all_images = []
    for img_path in all_normal_images:
        try:
            img = Image.open(img_path).convert('L')
            img = resize_with_aspect_ratio(img, img_size)
            img_array = np.array(img)

            # Áp dụng CLAHE
            img_array = apply_clahe(img_array)

            # Áp dụng advanced normalization
            img_array = advanced_normalize(img_array)

            all_images.append(img_array)
        except Exception as e:
            print(f"Lỗi khi xử lý ảnh {img_path}: {e}")

    all_images = np.array(all_images)
    all_images = np.expand_dims(all_images, axis=-1)

    # K-fold Cross Validation
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=42)
    cv_results = []
    best_val_loss = float('inf')
    best_model = None

    for fold, (train_idx, val_idx) in enumerate(kf.split(all_images)):
        print(f"\n{'='*50}")
        print(f"FOLD {fold + 1}/{n_splits}")
        print(f"{'='*50}")

        # Chia dữ liệu
        train_images = all_images[train_idx]
        val_images = all_images[val_idx]

        print(f"Train samples: {len(train_images)}, Val samples: {len(val_images)}")

        # Tạo data generators
        train_datagen = ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.15,
            height_shift_range=0.15,
            zoom_range=0.15,
            horizontal_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='constant',
            cval=0.0
        )

        val_datagen = ImageDataGenerator()

        train_generator = train_datagen.flow(train_images, train_images, batch_size=batch_size)
        val_generator = val_datagen.flow(val_images, val_images, batch_size=batch_size)

        # Xây dựng mô hình mới cho fold này
        model = build_autoencoder(input_shape=(img_size, img_size, 1))

        # Callbacks cho fold này
        fold_checkpoint_path = os.path.join(output_dir, f'autoencoder_fold_{fold+1}.h5')
        callbacks = get_callbacks(fold_checkpoint_path)

        # Huấn luyện
        history = model.fit(
            train_generator,
            steps_per_epoch=len(train_generator),
            epochs=epochs,
            validation_data=val_generator,
            validation_steps=len(val_generator),
            callbacks=callbacks,
            verbose=1
        )

        # Lưu kết quả fold
        fold_result = {
            'fold': fold + 1,
            'final_train_loss': float(history.history['loss'][-1]),
            'final_val_loss': float(history.history['val_loss'][-1]),
            'min_val_loss': float(min(history.history['val_loss'])),
            'history': {
                'loss': [float(x) for x in history.history['loss']],
                'val_loss': [float(x) for x in history.history['val_loss']],
                'mae': [float(x) for x in history.history['mae']],
                'val_mae': [float(x) for x in history.history['val_mae']]
            }
        }
        cv_results.append(fold_result)

        # Kiểm tra xem có phải model tốt nhất không
        if fold_result['min_val_loss'] < best_val_loss:
            best_val_loss = fold_result['min_val_loss']
            best_model = model
            # Lưu model tốt nhất
            best_model.save(os.path.join(output_dir, 'autoencoder_best_cv.h5'))
            print(f"Model tốt nhất mới từ fold {fold + 1} với val_loss: {best_val_loss:.6f}")

    return best_model, cv_results

def calculate_reconstruction_threshold(model, val_images, percentile=95):
    """
    Tính toán ngưỡng phát hiện bất thường dựa trên phân phối lỗi tái tạo

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation
        percentile: Phần trăm ngưỡng (mặc định 95%)

    Returns:
        threshold: Giá trị ngưỡng
    """
    # Dự đoán trên tập validation
    reconstructions = model.predict(val_images)

    # Tính MSE cho từng ảnh
    mse = np.mean(np.square(val_images - reconstructions), axis=(1, 2, 3))

    # Xác định ngưỡng ở percentile
    threshold = np.percentile(mse, percentile)

    return threshold

def visualize_results(model, val_images, num_samples=5):
    """
    Hiển thị kết quả tái tạo của autoencoder

    Args:
        model: Mô hình autoencoder đã train
        val_images: Tập validation
        num_samples: Số lượng mẫu hiển thị
    """
    # Lấy một số mẫu từ tập validation
    indices = np.random.randint(0, len(val_images), num_samples)
    samples = val_images[indices]

    # Dự đoán (tái tạo)
    reconstructions = model.predict(samples)

    # Hiển thị kết quả
    plt.figure(figsize=(15, 4))
    for i in range(num_samples):
        # Ảnh gốc
        plt.subplot(3, num_samples, i + 1)
        plt.imshow(samples[i].reshape(samples.shape[1], samples.shape[2]), cmap='gray')
        plt.title('Ảnh gốc')
        plt.axis('off')

        # Ảnh tái tạo
        plt.subplot(3, num_samples, i + 1 + num_samples)
        plt.imshow(reconstructions[i].reshape(reconstructions.shape[1], reconstructions.shape[2]), cmap='gray')
        plt.title('Ảnh tái tạo')
        plt.axis('off')

        # Sự khác biệt
        plt.subplot(3, num_samples, i + 1 + 2*num_samples)
        diff = np.abs(samples[i] - reconstructions[i]).reshape(samples.shape[1], samples.shape[2])
        plt.imshow(diff, cmap='jet')
        plt.title('Sự khác biệt')
        plt.axis('off')

    plt.tight_layout()
    plt.savefig('reconstruction_results.png')
    plt.close()

def plot_cv_results(cv_results, output_dir):
    """
    Vẽ biểu đồ kết quả Cross Validation

    Args:
        cv_results: Kết quả từ K-fold CV
        output_dir: Thư mục lưu biểu đồ
    """
    n_folds = len(cv_results)

    plt.figure(figsize=(20, 12))

    # 1. Training và Validation Loss cho từng fold
    plt.subplot(2, 3, 1)
    for i, result in enumerate(cv_results):
        epochs = range(1, len(result['history']['loss']) + 1)
        plt.plot(epochs, result['history']['loss'], label=f'Fold {i+1} Train', alpha=0.7)
        plt.plot(epochs, result['history']['val_loss'], label=f'Fold {i+1} Val', alpha=0.7, linestyle='--')

    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.title('Training và Validation Loss - Tất cả Folds')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)

    # 2. MAE cho từng fold
    plt.subplot(2, 3, 2)
    for i, result in enumerate(cv_results):
        epochs = range(1, len(result['history']['mae']) + 1)
        plt.plot(epochs, result['history']['mae'], label=f'Fold {i+1} Train', alpha=0.7)
        plt.plot(epochs, result['history']['val_mae'], label=f'Fold {i+1} Val', alpha=0.7, linestyle='--')

    plt.xlabel('Epochs')
    plt.ylabel('MAE')
    plt.title('Mean Absolute Error - Tất cả Folds')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)

    # 3. Final metrics comparison
    plt.subplot(2, 3, 3)
    folds = [f"Fold {i+1}" for i in range(n_folds)]
    final_train_losses = [result['final_train_loss'] for result in cv_results]
    final_val_losses = [result['final_val_loss'] for result in cv_results]
    min_val_losses = [result['min_val_loss'] for result in cv_results]

    x = np.arange(len(folds))
    width = 0.25

    plt.bar(x - width, final_train_losses, width, label='Final Train Loss', alpha=0.8)
    plt.bar(x, final_val_losses, width, label='Final Val Loss', alpha=0.8)
    plt.bar(x + width, min_val_losses, width, label='Min Val Loss', alpha=0.8)

    plt.xlabel('Folds')
    plt.ylabel('Loss')
    plt.title('So sánh Loss giữa các Folds')
    plt.xticks(x, folds)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Average loss curves
    plt.subplot(2, 3, 4)
    max_epochs = max(len(result['history']['loss']) for result in cv_results)

    avg_train_loss = []
    avg_val_loss = []
    std_train_loss = []
    std_val_loss = []

    for epoch in range(max_epochs):
        train_losses = []
        val_losses = []

        for result in cv_results:
            if epoch < len(result['history']['loss']):
                train_losses.append(result['history']['loss'][epoch])
                val_losses.append(result['history']['val_loss'][epoch])

        if train_losses:
            avg_train_loss.append(np.mean(train_losses))
            avg_val_loss.append(np.mean(val_losses))
            std_train_loss.append(np.std(train_losses))
            std_val_loss.append(np.std(val_losses))

    epochs = range(1, len(avg_train_loss) + 1)

    plt.plot(epochs, avg_train_loss, label='Avg Train Loss', linewidth=2)
    plt.fill_between(epochs,
                     np.array(avg_train_loss) - np.array(std_train_loss),
                     np.array(avg_train_loss) + np.array(std_train_loss),
                     alpha=0.2)

    plt.plot(epochs, avg_val_loss, label='Avg Val Loss', linewidth=2)
    plt.fill_between(epochs,
                     np.array(avg_val_loss) - np.array(std_val_loss),
                     np.array(avg_val_loss) + np.array(std_val_loss),
                     alpha=0.2)

    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.title('Average Loss với Standard Deviation')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. Statistics summary
    plt.subplot(2, 3, 5)
    plt.axis('off')

    # Tính toán statistics
    avg_final_train = np.mean(final_train_losses)
    std_final_train = np.std(final_train_losses)
    avg_final_val = np.mean(final_val_losses)
    std_final_val = np.std(final_val_losses)
    avg_min_val = np.mean(min_val_losses)
    std_min_val = np.std(min_val_losses)

    best_fold = np.argmin(min_val_losses) + 1

    stats_text = f"""
    THỐNG KÊ CROSS VALIDATION:

    Số folds: {n_folds}

    Final Train Loss:
    - Trung bình: {avg_final_train:.6f}
    - Độ lệch chuẩn: {std_final_train:.6f}

    Final Val Loss:
    - Trung bình: {avg_final_val:.6f}
    - Độ lệch chuẩn: {std_final_val:.6f}

    Min Val Loss:
    - Trung bình: {avg_min_val:.6f}
    - Độ lệch chuẩn: {std_min_val:.6f}

    Fold tốt nhất: {best_fold}
    Min Val Loss tốt nhất: {min(min_val_losses):.6f}
    """

    plt.text(0.1, 0.9, stats_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top', fontfamily='monospace')

    # 6. Box plot của losses
    plt.subplot(2, 3, 6)
    loss_data = [final_train_losses, final_val_losses, min_val_losses]
    labels = ['Final Train', 'Final Val', 'Min Val']

    plt.boxplot(loss_data, labels=labels)
    plt.ylabel('Loss')
    plt.title('Phân bố Loss giữa các Folds')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'cv_results.png'), dpi=300, bbox_inches='tight')
    plt.close()

def plot_enhanced_training_history(history, output_dir):
    """
    Vẽ biểu đồ training history cải tiến

    Args:
        history: Training history từ model.fit()
        output_dir: Thư mục lưu biểu đồ
    """
    plt.figure(figsize=(16, 10))

    # 1. Loss curves
    plt.subplot(2, 3, 1)
    plt.plot(history.history['loss'], label='Training Loss', linewidth=2)
    plt.plot(history.history['val_loss'], label='Validation Loss', linewidth=2)
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. MAE curves
    plt.subplot(2, 3, 2)
    plt.plot(history.history['mae'], label='Training MAE', linewidth=2)
    plt.plot(history.history['val_mae'], label='Validation MAE', linewidth=2)
    plt.title('Mean Absolute Error')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. Learning rate (nếu có)
    plt.subplot(2, 3, 3)
    if 'lr' in history.history:
        plt.plot(history.history['lr'], linewidth=2, color='red')
        plt.title('Learning Rate')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.yscale('log')
        plt.grid(True, alpha=0.3)
    else:
        plt.text(0.5, 0.5, 'Learning Rate\ndata not available',
                ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Learning Rate')

    # 4. Loss difference
    plt.subplot(2, 3, 4)
    loss_diff = np.array(history.history['val_loss']) - np.array(history.history['loss'])
    plt.plot(loss_diff, linewidth=2, color='purple')
    plt.title('Validation - Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss Difference')
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    plt.grid(True, alpha=0.3)

    # 5. Training summary
    plt.subplot(2, 3, 5)
    plt.axis('off')

    final_train_loss = history.history['loss'][-1]
    final_val_loss = history.history['val_loss'][-1]
    min_val_loss = min(history.history['val_loss'])
    min_val_epoch = np.argmin(history.history['val_loss']) + 1
    total_epochs = len(history.history['loss'])

    summary_text = f"""
    TỔNG KẾT TRAINING:

    Tổng số epochs: {total_epochs}

    Final Training Loss: {final_train_loss:.6f}
    Final Validation Loss: {final_val_loss:.6f}

    Best Validation Loss: {min_val_loss:.6f}
    Best epoch: {min_val_epoch}

    Overfitting check:
    Loss difference: {final_val_loss - final_train_loss:.6f}
    """

    if final_val_loss - final_train_loss > 0.01:
        summary_text += "\n⚠️  Có dấu hiệu overfitting"
    else:
        summary_text += "\n✅  Không có dấu hiệu overfitting"

    plt.text(0.1, 0.9, summary_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top', fontfamily='monospace')

    # 6. Smoothed curves
    plt.subplot(2, 3, 6)

    # Simple moving average
    window = max(1, len(history.history['loss']) // 10)

    def smooth(data, window):
        return np.convolve(data, np.ones(window)/window, mode='valid')

    if len(history.history['loss']) > window:
        smooth_train = smooth(history.history['loss'], window)
        smooth_val = smooth(history.history['val_loss'], window)
        smooth_epochs = range(window, len(history.history['loss']) + 1)

        plt.plot(smooth_epochs, smooth_train, label='Smoothed Training Loss', linewidth=2)
        plt.plot(smooth_epochs, smooth_val, label='Smoothed Validation Loss', linewidth=2)
        plt.title(f'Smoothed Loss (window={window})')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
    else:
        plt.text(0.5, 0.5, 'Not enough data\nfor smoothing',
                ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Smoothed Loss')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'enhanced_training_history.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Huấn luyện Autoencoder để phát hiện bất thường trong ảnh X-quang ngực')
    parser.add_argument('--data_dir', type=str, default='c:\\\\DeepLearning\\\\AE_Xray\\\\backend\\\\dataset', help="Đường dẫn đến thư mục cha chứa 'train' và 'test'")
    parser.add_argument('--img_size', type=int, default=128, help='Kích thước ảnh')
    parser.add_argument('--batch_size', type=int, default=32, help='Kích thước batch')
    parser.add_argument('--epochs', type=int, default=50, help='Số epoch')
    parser.add_argument('--output_dir', type=str, default='./saved_model', help='Thư mục lưu mô hình')
    parser.add_argument('--use_kfold', action='store_true', help='Sử dụng K-fold Cross Validation')
    parser.add_argument('--n_splits', type=int, default=5, help='Số folds cho K-fold CV')

    args = parser.parse_args()

    # Tạo thư mục đầu ra nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)

    if args.use_kfold:
        print("Sử dụng K-fold Cross Validation...")
        print(f"Số folds: {args.n_splits}")

        # Huấn luyện với K-fold CV
        best_model, cv_results = train_with_kfold_cv(
            args.data_dir,
            img_size=args.img_size,
            batch_size=args.batch_size,
            epochs=args.epochs,
            n_splits=args.n_splits,
            output_dir=args.output_dir
        )

        # Lưu kết quả CV
        import json
        cv_results_path = os.path.join(args.output_dir, 'cv_results.json')
        with open(cv_results_path, 'w') as f:
            json.dump(cv_results, f, indent=2)
        print(f"Kết quả CV đã lưu tại: {cv_results_path}")

        # Vẽ biểu đồ CV results
        plot_cv_results(cv_results, args.output_dir)
        print(f"Biểu đồ CV đã lưu tại: {os.path.join(args.output_dir, 'cv_results.png')}")

        # Sử dụng model tốt nhất
        autoencoder = best_model

        # Load dữ liệu validation để tính threshold
        _, _, val_images = load_and_preprocess_data(
            args.data_dir,
            img_size=args.img_size,
            batch_size=args.batch_size
        )

    else:
        print("Sử dụng training thông thường...")

        # Tải và tiền xử lý dữ liệu
        train_generator, val_generator, val_images = load_and_preprocess_data(
            args.data_dir,
            img_size=args.img_size,
            batch_size=args.batch_size
        )

        if train_generator is None:
            print("Thoát do không tìm thấy dữ liệu huấn luyện.")
            return

        # Xây dựng mô hình
        autoencoder = build_autoencoder(input_shape=(args.img_size, args.img_size, 1))
        print("Đang bắt đầu huấn luyện mô hình...")

        # Lấy callbacks
        callbacks = get_callbacks(os.path.join(args.output_dir, 'autoencoder.h5'))

        # Huấn luyện mô hình
        history = autoencoder.fit(
            train_generator,
            steps_per_epoch=len(train_generator),
            epochs=args.epochs,
            validation_data=val_generator,
            validation_steps=len(val_generator),
            callbacks=callbacks
        )

        # Tải mô hình tốt nhất
        autoencoder.load_weights(os.path.join(args.output_dir, 'autoencoder.h5'))

        # Vẽ biểu đồ training history cải tiến
        plot_enhanced_training_history(history, args.output_dir)
        print(f"Biểu đồ training history đã lưu tại: {os.path.join(args.output_dir, 'enhanced_training_history.png')}")

    # Tính toán ngưỡng
    threshold = calculate_reconstruction_threshold(autoencoder, val_images)
    print(f"Ngưỡng phát hiện bất thường: {threshold:.6f}")

    # Lưu ngưỡng
    with open(os.path.join(args.output_dir, 'threshold.txt'), 'w') as f:
        f.write(str(threshold))

    # Hiển thị kết quả
    visualize_results(autoencoder, val_images)

    print(f"Mô hình đã lưu tại: {os.path.join(args.output_dir, 'autoencoder.h5')}")
    print(f"Ngưỡng đã lưu tại: {os.path.join(args.output_dir, 'threshold.txt')}")

    print("\n" + "="*60)
    print("HOÀN THÀNH TRAINING VỚI CÁC CẢI TIẾN:")
    print("✅ CLAHE preprocessing cho ảnh X-ray")
    print("✅ Advanced normalization (MinMax + Z-score)")
    print("✅ Aspect ratio preservation")
    print("✅ Enhanced data augmentation")
    print("✅ U-Net style architecture với skip connections")
    print("✅ Deeper network (5 layers)")
    print("✅ Dropout regularization")
    print("✅ MSE loss function")
    print("✅ Learning rate scheduler")
    print("✅ Enhanced callbacks")
    if args.use_kfold:
        print("✅ K-fold Cross Validation")
        print("✅ Advanced training visualization")
    print("="*60)

if __name__ == '__main__':
    main()

import os
import numpy as np
import matplotlib.pyplot as plt
import glob
from PIL import Image
import argparse
import json
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, roc_curve, precision_recall_curve
from sklearn.model_selection import KFold
import seaborn as sns
from tensorflow.keras.models import load_model
import cv2

def apply_clahe(image):
    """
    Áp dụng CLAHE (Contrast Limited Adaptive Histogram Equalization) cho ảnh X-ray
    """
    if image.dtype != np.uint8:
        image = (image * 255).astype(np.uint8)

    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced_image = clahe.apply(image)

    return enhanced_image.astype(np.float32) / 255.0

def advanced_normalize(image):
    """
    Cải thiện normalization: MinMax + Z-score normalization
    """
    image = (image - image.min()) / (image.max() - image.min() + 1e-8)

    mean = image.mean()
    std = image.std()
    if std > 1e-8:
        image = (image - mean) / std

    image = np.clip(image, -3, 3)
    image = (image + 3) / 6

    return image

def resize_with_aspect_ratio(image, target_size):
    """
    Resize ảnh với việc bảo toàn tỷ lệ khung hình
    """
    width, height = image.size
    scale = min(target_size / width, target_size / height)

    new_width = int(width * scale)
    new_height = int(height * scale)

    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

    new_image = Image.new('L', (target_size, target_size), 0)
    paste_x = (target_size - new_width) // 2
    paste_y = (target_size - new_height) // 2
    new_image.paste(image, (paste_x, paste_y))

    return new_image

def load_test_data(data_dir, img_size=128):
    """
    Tải dữ liệu test từ cả NORMAL và PNEUMONIA

    Args:
        data_dir: Đường dẫn đến thư mục dataset
        img_size: Kích thước ảnh

    Returns:
        test_images: Mảng ảnh test
        test_labels: Nhãn (0: NORMAL, 1: PNEUMONIA)
        test_paths: Đường dẫn các file ảnh
    """
    # Lấy ảnh NORMAL từ test/NORMAL/
    normal_pattern = os.path.join(data_dir, 'test', 'NORMAL', '*.jpeg')
    normal_images = glob.glob(normal_pattern)

    # Lấy ảnh PNEUMONIA từ test/PNEUMONIA/
    pneumonia_pattern = os.path.join(data_dir, 'test', 'PNEUMONIA', '*.jpeg')
    pneumonia_images = glob.glob(pneumonia_pattern)

    if not normal_images and not pneumonia_images:
        print("Cảnh báo: Không tìm thấy ảnh test!")
        return None, None, None

    # Tải và tiền xử lý ảnh
    test_images = []
    test_labels = []
    test_paths = []

    # Tải ảnh NORMAL (label = 0) với preprocessing cải tiến
    for img_path in normal_images:
        try:
            img = Image.open(img_path).convert('L')
            img = resize_with_aspect_ratio(img, img_size)
            img_array = np.array(img)

            # Áp dụng CLAHE
            img_array = apply_clahe(img_array)

            # Áp dụng advanced normalization
            img_array = advanced_normalize(img_array)

            test_images.append(img_array)
            test_labels.append(0)  # NORMAL = 0
            test_paths.append(img_path)
        except Exception as e:
            print(f"Lỗi khi tải {img_path}: {e}")

    # Tải ảnh PNEUMONIA (label = 1) với preprocessing cải tiến
    for img_path in pneumonia_images:
        try:
            img = Image.open(img_path).convert('L')
            img = resize_with_aspect_ratio(img, img_size)
            img_array = np.array(img)

            # Áp dụng CLAHE
            img_array = apply_clahe(img_array)

            # Áp dụng advanced normalization
            img_array = advanced_normalize(img_array)

            test_images.append(img_array)
            test_labels.append(1)  # PNEUMONIA = 1
            test_paths.append(img_path)
        except Exception as e:
            print(f"Lỗi khi tải {img_path}: {e}")

    test_images = np.array(test_images)
    test_labels = np.array(test_labels)
    test_images = np.expand_dims(test_images, axis=-1)

    print(f"Tổng số ảnh test: {len(test_images)} (NORMAL: {np.sum(test_labels == 0)}, PNEUMONIA: {np.sum(test_labels == 1)})")

    return test_images, test_labels, test_paths

def calculate_reconstruction_errors(model, images):
    """
    Tính lỗi tái tạo cho từng ảnh

    Args:
        model: Mô hình autoencoder
        images: Mảng ảnh

    Returns:
        reconstruction_errors: Mảng lỗi tái tạo (MSE)
    """
    reconstructions = model.predict(images, verbose=0)
    mse = np.mean(np.square(images - reconstructions), axis=(1, 2, 3))
    return mse

def find_optimal_threshold(model, val_images, val_labels):
    """
    Tìm threshold tối ưu sử dụng ROC curve và cross-validation

    Args:
        model: Mô hình autoencoder
        val_images: Ảnh validation
        val_labels: Nhãn validation

    Returns:
        optimal_threshold: Threshold tối ưu
        metrics: Dictionary chứa thông tin về threshold selection
    """
    # Tính reconstruction errors
    reconstruction_errors = calculate_reconstruction_errors(model, val_images)

    # Tính ROC curve
    fpr, tpr, thresholds = roc_curve(val_labels, reconstruction_errors)

    # Tìm threshold tối ưu bằng Youden's J statistic (tpr - fpr)
    j_scores = tpr - fpr
    optimal_idx = np.argmax(j_scores)
    optimal_threshold_roc = thresholds[optimal_idx]

    # Tính Precision-Recall curve
    precision, recall, pr_thresholds = precision_recall_curve(val_labels, reconstruction_errors)

    # Tìm threshold tối ưu bằng F1-score
    f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
    optimal_f1_idx = np.argmax(f1_scores)
    optimal_threshold_f1 = pr_thresholds[optimal_f1_idx] if optimal_f1_idx < len(pr_thresholds) else pr_thresholds[-1]

    # Cross-validation để validate threshold
    kf = KFold(n_splits=5, shuffle=True, random_state=42)
    cv_thresholds = []

    for train_idx, val_idx in kf.split(val_images):
        cv_val_images = val_images[val_idx]
        cv_val_labels = val_labels[val_idx]

        cv_errors = calculate_reconstruction_errors(model, cv_val_images)
        cv_fpr, cv_tpr, cv_thresholds_temp = roc_curve(cv_val_labels, cv_errors)
        cv_j_scores = cv_tpr - cv_fpr
        cv_optimal_idx = np.argmax(cv_j_scores)
        cv_thresholds.append(cv_thresholds_temp[cv_optimal_idx])

    # Threshold trung bình từ cross-validation
    cv_threshold = np.mean(cv_thresholds)

    # Chọn threshold cuối cùng (trung bình của các phương pháp)
    final_threshold = np.mean([optimal_threshold_roc, optimal_threshold_f1, cv_threshold])

    metrics = {
        'roc_threshold': float(optimal_threshold_roc),
        'f1_threshold': float(optimal_threshold_f1),
        'cv_threshold': float(cv_threshold),
        'final_threshold': float(final_threshold),
        'roc_auc': float(roc_auc_score(val_labels, reconstruction_errors)),
        'max_j_score': float(j_scores[optimal_idx]),
        'max_f1_score': float(f1_scores[optimal_f1_idx])
    }

    return final_threshold, metrics

def evaluate_model(model, test_images, test_labels, threshold):
    """
    Đánh giá mô hình trên tập test

    Args:
        model: Mô hình autoencoder
        test_images: Ảnh test
        test_labels: Nhãn thực tế
        threshold: Ngưỡng phát hiện bất thường

    Returns:
        metrics: Dictionary chứa các metrics đánh giá
    """
    # Tính lỗi tái tạo
    reconstruction_errors = calculate_reconstruction_errors(model, test_images)

    # Dự đoán: lỗi > threshold => PNEUMONIA (1), ngược lại => NORMAL (0)
    predictions = (reconstruction_errors > threshold).astype(int)

    # Tính các metrics
    accuracy = accuracy_score(test_labels, predictions)
    precision = precision_score(test_labels, predictions)
    recall = recall_score(test_labels, predictions)
    f1 = f1_score(test_labels, predictions)

    # ROC-AUC sử dụng reconstruction error làm score
    roc_auc = roc_auc_score(test_labels, reconstruction_errors)

    # Confusion Matrix
    cm = confusion_matrix(test_labels, predictions)

    metrics = {
        'accuracy': float(accuracy),
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1),
        'roc_auc': float(roc_auc),
        'confusion_matrix': cm.tolist(),
        'threshold': float(threshold),
        'total_samples': len(test_labels),
        'normal_samples': int(np.sum(test_labels == 0)),
        'pneumonia_samples': int(np.sum(test_labels == 1)),
        'true_positives': int(cm[1, 1]),
        'true_negatives': int(cm[0, 0]),
        'false_positives': int(cm[0, 1]),
        'false_negatives': int(cm[1, 0])
    }

    return metrics, reconstruction_errors, predictions

def plot_evaluation_results(test_labels, reconstruction_errors, predictions, threshold, output_dir):
    """
    Vẽ biểu đồ kết quả đánh giá cải tiến với nhiều metrics hơn
    """
    plt.figure(figsize=(20, 15))

    # 1. Histogram của reconstruction errors
    plt.subplot(3, 3, 1)
    normal_errors = reconstruction_errors[test_labels == 0]
    pneumonia_errors = reconstruction_errors[test_labels == 1]

    plt.hist(normal_errors, bins=50, alpha=0.7, label='Bình thường', color='blue')
    plt.hist(pneumonia_errors, bins=50, alpha=0.7, label='Viêm phổi', color='red')
    plt.axvline(threshold, color='black', linestyle='--', label=f'Ngưỡng: {threshold:.4f}')
    plt.xlabel('Lỗi Tái Tạo (MSE)')
    plt.ylabel('Tần Số')
    plt.title('Phân Bố Lỗi Tái Tạo')
    plt.legend()

    # 2. Confusion Matrix
    plt.subplot(3, 3, 2)
    cm = confusion_matrix(test_labels, predictions)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Bình thường', 'Viêm phổi'],
                yticklabels=['Bình thường', 'Viêm phổi'])
    plt.title('Ma Trận Nhầm Lẫn')
    plt.ylabel('Nhãn Thực Tế')
    plt.xlabel('Nhãn Dự Đoán')

    # 3. ROC Curve
    fpr, tpr, _ = roc_curve(test_labels, reconstruction_errors)
    plt.subplot(3, 3, 3)
    plt.plot(fpr, tpr, label=f'Đường Cong ROC (AUC = {roc_auc_score(test_labels, reconstruction_errors):.3f})')
    plt.plot([0, 1], [0, 1], 'k--', label='Ngẫu nhiên')
    plt.xlabel('Tỷ Lệ Dương Tính Giả (FPR)')
    plt.ylabel('Tỷ Lệ Dương Tính Thật (TPR)')
    plt.title('Đường Cong ROC')
    plt.legend()

    # 4. Precision-Recall Curve
    precision, recall, _ = precision_recall_curve(test_labels, reconstruction_errors)
    plt.subplot(3, 3, 4)
    plt.plot(recall, precision, label=f'PR Curve')
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Đường Cong Precision-Recall')
    plt.legend()

    # 5. Metrics Bar Chart
    plt.subplot(3, 3, 5)
    metrics_names = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    metrics_values = [
        accuracy_score(test_labels, predictions),
        precision_score(test_labels, predictions),
        recall_score(test_labels, predictions),
        f1_score(test_labels, predictions)
    ]
    bars = plt.bar(metrics_names, metrics_values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
    plt.title('Các Metrics Đánh Giá')
    plt.ylabel('Giá Trị')
    plt.ylim(0, 1)

    # Thêm giá trị lên các cột
    for bar, value in zip(bars, metrics_values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')

    # 6. Error Distribution by Class
    plt.subplot(3, 3, 6)
    plt.boxplot([normal_errors, pneumonia_errors], labels=['Bình thường', 'Viêm phổi'])
    plt.axhline(threshold, color='red', linestyle='--', label=f'Ngưỡng: {threshold:.4f}')
    plt.ylabel('Lỗi Tái Tạo (MSE)')
    plt.title('Phân Bố Lỗi Theo Lớp')
    plt.legend()

    # 7. Per-class Analysis
    plt.subplot(3, 3, 7)
    tn, fp, fn, tp = cm.ravel()
    class_metrics = {
        'True Negatives': tn,
        'False Positives': fp,
        'False Negatives': fn,
        'True Positives': tp
    }
    plt.bar(class_metrics.keys(), class_metrics.values(),
            color=['green', 'orange', 'red', 'blue'])
    plt.title('Phân Tích Chi Tiết Kết Quả')
    plt.ylabel('Số Lượng')
    plt.xticks(rotation=45)

    # 8. Threshold Analysis
    plt.subplot(3, 3, 8)
    thresholds_range = np.linspace(reconstruction_errors.min(), reconstruction_errors.max(), 100)
    f1_scores = []

    for thresh in thresholds_range:
        pred_temp = (reconstruction_errors > thresh).astype(int)
        f1_temp = f1_score(test_labels, pred_temp)
        f1_scores.append(f1_temp)

    plt.plot(thresholds_range, f1_scores, label='F1-Score')
    plt.axvline(threshold, color='red', linestyle='--', label=f'Ngưỡng hiện tại: {threshold:.4f}')
    plt.xlabel('Threshold')
    plt.ylabel('F1-Score')
    plt.title('F1-Score vs Threshold')
    plt.legend()

    # 9. Summary Statistics
    plt.subplot(3, 3, 9)
    plt.axis('off')
    summary_text = f"""
    TỔNG KẾT ĐÁNH GIÁ:

    Tổng số mẫu: {len(test_labels)}
    - Bình thường: {np.sum(test_labels == 0)}
    - Viêm phổi: {np.sum(test_labels == 1)}

    Kết quả phân loại:
    - True Negatives: {tn}
    - False Positives: {fp}
    - False Negatives: {fn}
    - True Positives: {tp}

    Metrics chính:
    - Accuracy: {accuracy_score(test_labels, predictions):.3f}
    - Precision: {precision_score(test_labels, predictions):.3f}
    - Recall: {recall_score(test_labels, predictions):.3f}
    - F1-Score: {f1_score(test_labels, predictions):.3f}
    - ROC-AUC: {roc_auc_score(test_labels, reconstruction_errors):.3f}
    """
    plt.text(0.1, 0.9, summary_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top', fontfamily='monospace')

    # 4. Scatter plot của reconstruction errors
    plt.subplot(2, 3, 4)
    normal_indices = np.where(test_labels == 0)[0]
    pneumonia_indices = np.where(test_labels == 1)[0]

    plt.scatter(normal_indices, normal_errors, alpha=0.6, label='Bình thường', color='blue', s=10)
    plt.scatter(pneumonia_indices, pneumonia_errors, alpha=0.6, label='Viêm phổi', color='red', s=10)
    plt.axhline(threshold, color='black', linestyle='--', label=f'Ngưỡng: {threshold:.4f}')
    plt.xlabel('Chỉ Số Mẫu')
    plt.ylabel('Lỗi Tái Tạo')
    plt.title('Lỗi Tái Tạo Theo Mẫu')
    plt.legend()

    # 5. Tóm tắt metrics
    plt.subplot(2, 3, 5)
    accuracy = accuracy_score(test_labels, predictions)
    precision = precision_score(test_labels, predictions)
    recall = recall_score(test_labels, predictions)
    f1 = f1_score(test_labels, predictions)

    metrics_text = f"""
    Độ Chính Xác: {accuracy:.3f}
    Precision: {precision:.3f}
    Recall: {recall:.3f}
    F1-Score: {f1:.3f}
    ROC-AUC: {roc_auc_score(test_labels, reconstruction_errors):.3f}

    True Positives: {cm[1,1]}
    True Negatives: {cm[0,0]}
    False Positives: {cm[0,1]}
    False Negatives: {cm[1,0]}
    """

    plt.text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center')
    plt.axis('off')
    plt.title('Chỉ Số Đánh Giá')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'evaluation_results.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Đánh giá Autoencoder cho Phát hiện Bất thường Chụp X-quang Ngực')
    parser.add_argument('--data_dir', type=str, default='c:\\\\DeepLearning\\\\AE_Xray\\\\backend\\\\dataset',
                       help="Đường dẫn đến thư mục dataset")
    parser.add_argument('--model_path', type=str, default='./saved_model/autoencoder.h5',
                       help='Đường dẫn đến mô hình đã huấn luyện')
    parser.add_argument('--threshold_path', type=str, default='./saved_model/threshold.txt',
                       help='Đường dẫn đến file ngưỡng')
    parser.add_argument('--output_dir', type=str, default='./saved_model',
                       help='Thư mục lưu kết quả đánh giá')
    parser.add_argument('--img_size', type=int, default=128, help='Kích thước ảnh')

    args = parser.parse_args()

    # Tạo thư mục output nếu chưa tồn tại
    os.makedirs(args.output_dir, exist_ok=True)

    # Tải mô hình
    try:
        model = load_model(args.model_path)
        print(f"Đã tải mô hình từ {args.model_path}")
    except Exception as e:
        print(f"Lỗi khi tải mô hình: {e}")
        return

    # Tải ngưỡng
    try:
        with open(args.threshold_path, 'r') as f:
            threshold = float(f.read().strip())
        print(f"Đã tải ngưỡng: {threshold}")
    except Exception as e:
        print(f"Lỗi khi tải ngưỡng: {e}")
        return

    # Tải dữ liệu test
    test_images, test_labels, test_paths = load_test_data(args.data_dir, args.img_size)
    if test_images is None:
        print("Không tìm thấy dữ liệu test. Thoát chương trình.")
        return

    # Đánh giá mô hình
    print("Đang đánh giá mô hình...")
    metrics, reconstruction_errors, predictions = evaluate_model(model, test_images, test_labels, threshold)

    # In kết quả
    print("\n" + "="*50)
    print("KẾT QUẢ ĐÁNH GIÁ")
    print("="*50)
    print(f"Độ chính xác: {metrics['accuracy']:.4f}")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1-Score: {metrics['f1_score']:.4f}")
    print(f"ROC-AUC: {metrics['roc_auc']:.4f}")
    print(f"Ngưỡng: {metrics['threshold']:.6f}")
    print(f"\nMa trận nhầm lẫn:")
    print(f"True Negatives: {metrics['true_negatives']}")
    print(f"False Positives: {metrics['false_positives']}")
    print(f"False Negatives: {metrics['false_negatives']}")
    print(f"True Positives: {metrics['true_positives']}")

    # Lưu metrics vào JSON
    metrics_path = os.path.join(args.output_dir, 'evaluation_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"\nĐã lưu metrics vào {metrics_path}")

    # Vẽ biểu đồ kết quả
    plot_evaluation_results(test_labels, reconstruction_errors, predictions, threshold, args.output_dir)
    print(f"Đã lưu biểu đồ đánh giá vào {os.path.join(args.output_dir, 'evaluation_results.png')}")

if __name__ == '__main__':
    main()

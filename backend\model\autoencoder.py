import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Conv2D, MaxPooling2D, UpSampling2D, BatchNormalization, Dropout, concatenate
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>

def build_autoencoder(input_shape=(128, 128, 1)):
    """
    X<PERSON>y dựng mô hình Autoencoder cải tiến với skip connections và kiến trúc sâu hơn

    Args:
        input_shape: <PERSON><PERSON><PERSON> thước ảnh đầu vào (mặc định 128x128 grayscale)

    Returns:
        model: Keras Model đã biên dịch
    """
    # Input
    input_img = Input(shape=input_shape)

    # Encoder với skip connections (U-Net style)
    # Block 1: 128x128 -> 64x64
    conv1 = Conv2D(32, (3, 3), activation='relu', padding='same')(input_img)
    conv1 = BatchNormalization()(conv1)
    conv1 = Conv2D(32, (3, 3), activation='relu', padding='same')(conv1)
    conv1 = BatchNormalization()(conv1)
    conv1 = Dropout(0.2)(conv1)
    pool1 = MaxPooling2D((2, 2), padding='same')(conv1)

    # Block 2: 64x64 -> 32x32
    conv2 = Conv2D(64, (3, 3), activation='relu', padding='same')(pool1)
    conv2 = BatchNormalization()(conv2)
    conv2 = Conv2D(64, (3, 3), activation='relu', padding='same')(conv2)
    conv2 = BatchNormalization()(conv2)
    conv2 = Dropout(0.2)(conv2)
    pool2 = MaxPooling2D((2, 2), padding='same')(conv2)

    # Block 3: 32x32 -> 16x16
    conv3 = Conv2D(128, (3, 3), activation='relu', padding='same')(pool2)
    conv3 = BatchNormalization()(conv3)
    conv3 = Conv2D(128, (3, 3), activation='relu', padding='same')(conv3)
    conv3 = BatchNormalization()(conv3)
    conv3 = Dropout(0.3)(conv3)
    pool3 = MaxPooling2D((2, 2), padding='same')(conv3)

    # Block 4: 16x16 -> 8x8
    conv4 = Conv2D(256, (3, 3), activation='relu', padding='same')(pool3)
    conv4 = BatchNormalization()(conv4)
    conv4 = Conv2D(256, (3, 3), activation='relu', padding='same')(conv4)
    conv4 = BatchNormalization()(conv4)
    conv4 = Dropout(0.3)(conv4)
    pool4 = MaxPooling2D((2, 2), padding='same')(conv4)

    # Bottleneck: 8x8 -> 8x8
    conv5 = Conv2D(512, (3, 3), activation='relu', padding='same')(pool4)
    conv5 = BatchNormalization()(conv5)
    conv5 = Conv2D(512, (3, 3), activation='relu', padding='same')(conv5)
    conv5 = BatchNormalization()(conv5)
    conv5 = Dropout(0.4)(conv5)

    # Decoder với skip connections
    # Block 6: 8x8 -> 16x16
    up6 = UpSampling2D((2, 2))(conv5)
    up6 = concatenate([up6, conv4])  # Skip connection
    conv6 = Conv2D(256, (3, 3), activation='relu', padding='same')(up6)
    conv6 = BatchNormalization()(conv6)
    conv6 = Conv2D(256, (3, 3), activation='relu', padding='same')(conv6)
    conv6 = BatchNormalization()(conv6)
    conv6 = Dropout(0.3)(conv6)

    # Block 7: 16x16 -> 32x32
    up7 = UpSampling2D((2, 2))(conv6)
    up7 = concatenate([up7, conv3])  # Skip connection
    conv7 = Conv2D(128, (3, 3), activation='relu', padding='same')(up7)
    conv7 = BatchNormalization()(conv7)
    conv7 = Conv2D(128, (3, 3), activation='relu', padding='same')(conv7)
    conv7 = BatchNormalization()(conv7)
    conv7 = Dropout(0.2)(conv7)

    # Block 8: 32x32 -> 64x64
    up8 = UpSampling2D((2, 2))(conv7)
    up8 = concatenate([up8, conv2])  # Skip connection
    conv8 = Conv2D(64, (3, 3), activation='relu', padding='same')(up8)
    conv8 = BatchNormalization()(conv8)
    conv8 = Conv2D(64, (3, 3), activation='relu', padding='same')(conv8)
    conv8 = BatchNormalization()(conv8)
    conv8 = Dropout(0.2)(conv8)

    # Block 9: 64x64 -> 128x128
    up9 = UpSampling2D((2, 2))(conv8)
    up9 = concatenate([up9, conv1])  # Skip connection
    conv9 = Conv2D(32, (3, 3), activation='relu', padding='same')(up9)
    conv9 = BatchNormalization()(conv9)
    conv9 = Conv2D(32, (3, 3), activation='relu', padding='same')(conv9)
    conv9 = BatchNormalization()(conv9)

    # Output layer
    decoded = Conv2D(1, (1, 1), activation='sigmoid', padding='same')(conv9)

    # Autoencoder với loss function cải tiến
    autoencoder = Model(input_img, decoded)

    # Sử dụng MSE thay vì binary_crossentropy và thêm MAE metric
    optimizer = Adam(learning_rate=0.001)
    autoencoder.compile(
        optimizer=optimizer,
        loss='mse',  # Mean Squared Error cho reconstruction
        metrics=['mae']  # Mean Absolute Error để theo dõi
    )

    return autoencoder

def get_callbacks(checkpoint_path):
    """
    Tạo callbacks cải tiến cho quá trình huấn luyện

    Args:
        checkpoint_path: Đường dẫn lưu model checkpoint

    Returns:
        list: Danh sách các callbacks
    """
    # Model checkpoint để lưu model tốt nhất
    checkpointer = ModelCheckpoint(
        filepath=checkpoint_path,
        monitor='val_loss',
        verbose=1,
        save_best_only=True,
        save_weights_only=False
    )

    # Early stopping với patience tăng lên
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=15,  # Tăng patience để tránh dừng sớm
        restore_best_weights=True,
        verbose=1
    )

    # Learning rate scheduler
    lr_scheduler = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,  # Giảm learning rate xuống 50%
        patience=5,  # Đợi 5 epochs không cải thiện
        min_lr=1e-7,  # Learning rate tối thiểu
        verbose=1
    )

    return [checkpointer, early_stopping, lr_scheduler]
@echo off
chcp 65001 > nul
echo ========================================
echo    HUAN LUYEN VA DANH GIA MO HINH CAI TIEN
echo ========================================
echo.

cd backend\model

echo ========================================
echo    CAC CAI TIEN DA DUOC TRIEN KHAI:
echo ========================================
echo ✅ CLAHE preprocessing cho anh X-ray
echo ✅ Advanced normalization (MinMax + Z-score)
echo ✅ Aspect ratio preservation
echo ✅ Enhanced data augmentation
echo ✅ U-Net style architecture voi skip connections
echo ✅ Deeper network (5 layers)
echo ✅ Dropout regularization
echo ✅ MSE loss function
echo ✅ Learning rate scheduler
echo ✅ Enhanced callbacks
echo ✅ K-fold Cross Validation (tuy chon)
echo ✅ Auto-threshold optimization (tuy chon)
echo ✅ Advanced training visualization
echo ========================================
echo.

echo Dang huan luyen mo hinh voi du lieu NORMAL...
echo - Su dung train/NORMAL/ cho huan luyen
echo - Su dung val/NORMAL/ cho validation
echo - Ap dung tat ca cac cai tien preprocessing va architecture
echo.

set PYTHONIOENCODING=utf-8

echo ========================================
echo    BUOC 1: HUAN LUYEN MO HINH CAI TIEN
echo ========================================
python train.py --data_dir "..\..\backend\dataset" --img_size 128 --batch_size 32 --epochs 50 --output_dir "saved_model"

if %errorlevel% neq 0 (
    echo.
    echo [LOI] Huan luyen that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    BUOC 2: DANH GIA VOI AUTO-THRESHOLD
echo ========================================
echo.

python evaluate.py --data_dir "..\..\backend\dataset" --model_path "saved_model\autoencoder.h5" --threshold_path "saved_model\threshold.txt" --output_dir "saved_model" --auto_threshold

if %errorlevel% neq 0 (
    echo.
    echo [LOI] Danh gia that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo        HOAN THANH TOAN BO QUY TRINH
echo ========================================
echo.
echo KET QUA HUAN LUYEN CAI TIEN:
echo - Mo hinh: backend\model\saved_model\autoencoder.h5
echo - Nguong phat hien toi uu: backend\model\saved_model\threshold.txt
echo - Bieu do training: backend\model\saved_model\enhanced_training_history.png
echo - Ket qua reconstruction: backend\model\reconstruction_results.png
echo.
echo KET QUA DANH GIA CAI TIEN:
echo - Metrics: backend\model\saved_model\evaluation_metrics.json
echo - Bieu do evaluation: backend\model\saved_model\evaluation_results.png
echo - Threshold optimization: backend\model\saved_model\threshold_optimization.json
echo.
echo ========================================
echo    TUY CHON NANG CAO (CHAY RIENG):
echo ========================================
echo.
echo 1. K-fold Cross Validation:
echo    python train.py --use_kfold --n_splits 5 --epochs 30
echo.
echo 2. Hyperparameter tuning:
echo    python hyperparameter_tuning.py (neu co)
echo.
echo ========================================
echo.
echo Ban co the chay ung dung bang cach chay: run_app.bat
echo.

pause

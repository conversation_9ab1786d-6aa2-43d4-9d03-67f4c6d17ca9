@echo off
chcp 65001 > nul
echo ========================================
echo    K-FOLD CROSS VALIDATION TRAINING
echo ========================================
echo.

cd backend\model

echo ========================================
echo    THONG TIN K-FOLD CROSS VALIDATION:
echo ========================================
echo - Su dung 5-fold Cross Validation
echo - Ket hop tat ca du lieu NORMAL tu train/ va val/
echo - Huan luyen 5 mo hinh khac nhau
echo - Chon mo hinh tot nhat dua tren validation loss
echo - Tao bao cao chi tiet ve hieu suat cua tung fold
echo ========================================
echo.

echo Dang bat dau K-fold Cross Validation...
echo Quy trinh nay se mat nhieu thoi gian hon training thuong...
echo.

set PYTHONIOENCODING=utf-8

echo ========================================
echo    BUOC 1: K-FOLD CROSS VALIDATION
echo ========================================
python train.py --data_dir "..\..\backend\dataset" --img_size 128 --batch_size 32 --epochs 30 --output_dir "saved_model" --use_kfold --n_splits 5

if %errorlevel% neq 0 (
    echo.
    echo [LOI] K-fold training that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    BUOC 2: DANH GIA VOI AUTO-THRESHOLD
echo ========================================
echo.

python evaluate.py --data_dir "..\..\backend\dataset" --model_path "saved_model\autoencoder_best_cv.h5" --threshold_path "saved_model\threshold.txt" --output_dir "saved_model" --auto_threshold

if %errorlevel% neq 0 (
    echo.
    echo [LOI] Danh gia that bai!
    pause
    exit /b 1
)

echo.
echo ========================================
echo        HOAN THANH K-FOLD TRAINING
echo ========================================
echo.
echo KET QUA K-FOLD CROSS VALIDATION:
echo - Mo hinh tot nhat: backend\model\saved_model\autoencoder_best_cv.h5
echo - Ket qua CV: backend\model\saved_model\cv_results.json
echo - Bieu do CV: backend\model\saved_model\cv_results.png
echo - Nguong toi uu: backend\model\saved_model\threshold.txt
echo.
echo KET QUA DANH GIA:
echo - Metrics: backend\model\saved_model\evaluation_metrics.json
echo - Bieu do evaluation: backend\model\saved_model\evaluation_results.png
echo - Threshold optimization: backend\model\saved_model\threshold_optimization.json
echo.
echo ========================================
echo    THONG TIN QUAN TRONG:
echo ========================================
echo.
echo Mo hinh duoc huan luyen bang K-fold CV thuong co:
echo - Hieu suat on dinh hon
echo - Kha nang tong quat hoa tot hon
echo - Giam thieu overfitting
echo - Danh gia chinh xac hon ve hieu suat mo hinh
echo.
echo ========================================
echo.
echo Ban co the chay ung dung bang cach chay: run_app.bat
echo.

pause
